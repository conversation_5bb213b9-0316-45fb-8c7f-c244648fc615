import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {Text} from "@/src/components/Text.tsx";
import React, {Suspense, useCallback, useEffect, useMemo, useState} from "react";
import {CustomDateFilter, TimeFrameFilter} from "@/src/components/CustomDateFilter.tsx";
import {api} from "@/src/api.ts";
import {DateTime, WeekdayNumbers} from "luxon";
import {IsoCompleteDate} from "../../../../../../../../api/src/timeSchemas.ts";
import {z} from "zod";
import {getDateRangeForTimeFrame} from "@/src/utils/dateRange.util.ts";
import {Input} from "@/src/components/ui/input.tsx";
import {SearchIcon, PrinterIcon, FilterIcon} from 'lucide-react';
import debounce from 'lodash/debounce';
import {filter, map} from "lodash";
import {
  ColumnFiltersState,
  createColumnHelper,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  SortingState,
  Updater,
  useReactTable,
} from "@tanstack/react-table";
import {DataTable} from "@/src/components/ui/data-table.tsx";
import {DataTableColumnHeader} from "@/src/components/DataTableColumnHeader.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {SchedulePersonDto} from "../../../../../../../../api/src/schedulePersonDto.ts";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {useDebounceValue} from 'usehooks-ts';
import {
  calculateAvailableHours, filterPersonByName
} from "../../../../../../../../api/src/insights/scheduling.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {
  SchedulingInsightsPersonShiftStats,
  SchedulingInsightsPersonSwapStats
} from "../../../../../../../../api/src/insights/scheduling.schema.ts";
import {ExportScheduleDataSelectorDialog} from "@/src/components/insights/ExportScheduleDataSelectorDialog.tsx";
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {Switch} from "@/src/components/ui/switch.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {useForm} from "@tanstack/react-form";
import {Separator} from "@/src/components/ui/separator.tsx";

const schedulingInsightsDateFilterSchema = z.object({
  week: z.number().int().min(1).max(53).optional(),
  year: z.number().int().min(2000).max(9999).optional(),
  day: z.number().int().min(1).max(7).optional(),
  month: z.number().int().min(1).max(12).optional(),
  timeFrame: z.enum(['day', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(), // ISO date string for custom range
  endDate: z.string().optional(),   // ISO date string for custom range
  search: z.string().optional(),    // Team member search
  pageIndex: z.number().int().min(0).optional(),
  pageSize: z.number().int().min(1).max(100).optional(),
  // Sorting parameters
  sorting: z.array(z.object({
    id: z.string(),
    desc: z.boolean(),
  })).optional(),
  // Column filtering parameters
  columnFilters: z.array(z.object({
    id: z.string(),
    value: z.any(),
  })).optional(),
  // Filter parameters
  includeArchived: z.boolean().optional(),
}).optional();

type InsightsSearch = z.infer<typeof schedulingInsightsDateFilterSchema>;

const colHelper = createColumnHelper<{
  person: SchedulePersonDto,
  totalAvailability: number,
  shiftStats: SchedulingInsightsPersonShiftStats,
  swapStats: SchedulingInsightsPersonSwapStats,
  offerStats: SchedulingInsightsPersonSwapStats,
  timeOffHours: number,
}>();

// Rounds number and removes trailing zeros
const cleanNumber = (hours: number) => parseFloat(hours.toFixed(2));

interface FilterFormValues {
  includeArchived: boolean;
}

interface FilterButtonProps {
  initialValues: FilterFormValues;
  onSubmit: (values: FilterFormValues) => void;
  onReset: () => void;
}

const FilterButton: React.FC<FilterButtonProps> = ({ initialValues, onSubmit, onReset }) => {
  const popover = useDisclosure();

  const defaultValues: FilterFormValues = {
    includeArchived: false,
  };

  const form = useForm({
    defaultValues: {
      ...defaultValues,
      ...initialValues,
    },
    onSubmit: ({ value }) => {
      onSubmit(value);
      popover.setOpen(false);
    },
  });

  const resetForm = () => {
    form.store.setState((state) => ({
      ...state,
      values: defaultValues,
    }));
    onReset();
    onSubmit(defaultValues);
  };

  const resetToCurrentState = () => {
    form.store.setState((state) => ({
      ...state,
      values: {
        ...defaultValues,
        ...initialValues,
      },
    }));
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Reset form to current state when closing without applying
      resetToCurrentState();
    }
    popover.setOpen(open);
  };

  // Check if there are any active filters
  const hasActiveFilters = initialValues.includeArchived !== defaultValues.includeArchived;

  return (
    <Popover open={popover.isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <div className="relative">
          <Button variant="outline" leftIcon={<FilterIcon size={16} />}>
            Filter
          </Button>
          {hasActiveFilters && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="p-4 w-[300px]">
        <div className="flex flex-row justify-between items-center">
          <Text size="lg" semibold>Filters</Text>
          <Button variant="link" onClick={resetForm}>Reset</Button>
        </div>
        <Separator className="my-3" />

        <form.Field
          name="includeArchived"
          children={(field) => (
            <div className="flex items-center space-x-2 my-2">
              <Switch
                id={field.name}
                checked={field.state.value}
                onCheckedChange={field.handleChange}
              />
              <Label htmlFor={field.name}>Include Archived Team Members</Label>
            </div>
          )}
        />

        <Button className="w-full mt-4" onClick={form.handleSubmit}>
          Apply Filters
        </Button>
      </PopoverContent>
    </Popover>
  );
};

const columns = [
  colHelper.accessor(row => `${row.person.firstName} ${row.person.lastName}`, {
    id: 'fullName',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Team Member"}/>
    },
    cell: ({getValue, row}) => {
      const person = row.original.person;
      const fullName = getValue();
      return <div className={"flex flex-row gap-2 items-center"}>
        <PersonAvatar person={person}/>
        <div className={"flex flex-col gap-1"}>
          <Text>{fullName}</Text>
          <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme={"light-bg"} size={14}/>
        </div>
      </div>
    },
    filterFn: 'includesString',
  }),
  colHelper.accessor(row => row.totalAvailability, {
    id: 'availabilityHours',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Availability"}/>
    },
    cell: ({getValue}) => {
      const value = getValue();
      return <Text size={"sm"}>{cleanNumber(value)} hrs</Text>
    },
  }),
  colHelper.accessor(row => row.shiftStats.hours, {
    id: 'workedHours',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Scheduled"}/>
    },
    cell: ({getValue}) => {
      const value = cleanNumber(getValue());
      return <Text size={"sm"}>{value} hrs</Text>
    },
  }),
  colHelper.accessor(row => row.shiftStats.adminNonOps, {
    id: 'adminNonOpsHours',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Admin/Non-ops"}/>
    },
    cell: ({getValue}) => {
      const value = cleanNumber(getValue());
      return <Text size={"sm"}>{value} hrs</Text>
    },
  }),
  colHelper.accessor(row => row.shiftStats.shifts, {
    id: 'shiftsCount',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Shifts"}/>
    },
    cell: ({getValue}) => {
      const value = cleanNumber(getValue());
      return <Text size={"sm"}>{value}</Text>
    },
  }),
  colHelper.accessor(row => row.swapStats, {
    id: 'swapsCount',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Swaps"}/>
    },
    sortingFn: (rowA, rowB) => {
      return rowA.original.swapStats.offered - rowB.original.swapStats.offered;
    },
    cell: ({getValue}) => {
      const {offered, received} = getValue();
      return <div className={"flex flex-col"}>
        <Text size={"sm"}>{offered} Offered</Text>
        <Text size={"sm"} muted>{received} Pick-ups</Text>
      </div>
    },
  }),
  colHelper.accessor(row => row.offerStats, {
    id: 'offersCount',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Offers"}/>
    },
    sortingFn: (rowA, rowB) => {
      return rowA.original.offerStats.offered - rowB.original.offerStats.offered;
    },
    cell: ({getValue}) => {
      const {offered, received} = getValue();
      return <div className={"flex flex-col"}>
        <Text size={"sm"}>{offered} Offered</Text>
        <Text size={"sm"} muted>{received} Pick-ups</Text>
      </div>
    },
  }),
  colHelper.accessor(row => row.timeOffHours, {
    id: 'timeOff',
    header: ({column}) => {
      return <DataTableColumnHeader column={column} title={"Time Off"}/>
    },
    cell: ({getValue}) => {
      const totalHours = getValue();
      return <div>
        <Text size={"sm"}>{cleanNumber(totalHours)} hrs</Text>
      </div>
    },
  }),
];

export const Route = createFileRoute(
        '/_signedin/$businessId/$storeId/_nav/schedules/insights',
)({
  component: () => (
          <Suspense fallback={<div>Loading...</div>}>
            <RouteComponent/>
          </Suspense>
  ),
  validateSearch: (search: unknown & SearchSchemaInput): InsightsSearch | undefined => {
    if (schedulingInsightsDateFilterSchema.safeParse(search).success) {
      return search as never
    }
  },
})

function RouteComponent() {
  const {businessId, storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = Route.useNavigate();

  const [[store, {people}]] = api.useSuspenseQueries(t => [
    t.user.getStoreAdmin({storeId: storeId!}),
    t.user.getAllSchedulePeopleAtStore({storeId: storeId, includeArchived: true, includeSuspended: true}, {
      staleTime: 1000 * 60 * 60
    })
  ]);

  const schedulePeople = useMemo(() => {
    if (searchParams?.includeArchived) {
      return people;
    }
    return filter(people, p => p.status === "Active");
  }, [people, searchParams]);

  const timezone = store.timezone ?? "America/New_York";

  const [today] = useState(DateTime.now().setZone(timezone));

  // Date filter logic with default to weekly view for current week
  const timeFrame: TimeFrameFilter = (searchParams?.timeFrame as TimeFrameFilter) ?? 'week';
  const isoDateSearch: IsoCompleteDate = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    day: searchParams.day,
    month: searchParams.month
  } : {week: today.weekNumber, year: today.weekYear};

  // Custom date range logic - only used when timeFrame is explicitly set to 'custom'
  let customStartDate: IsoCompleteDate | null = null;
  let customEndDate: IsoCompleteDate | null = null;

  if (searchParams?.startDate) {
    // Parse as calendar date (YYYY-MM-DD) in the store's timezone
    const [year, month, day] = map(searchParams.startDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customStartDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }

  if (searchParams?.endDate) {
    // Parse as calendar date (YYYY-MM-DD) in the store's timezone
    const [year, month, day] = map(searchParams.endDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customEndDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }
  // Pagination state from URL search params
  const pageIndex = searchParams?.pageIndex ?? 0;
  const pageSize = searchParams?.pageSize ?? 50;

  // Date navigation function
  const handleDateChange = (date: IsoCompleteDate, timeFrame: TimeFrameFilter, customStart?: IsoCompleteDate | null, customEnd?: IsoCompleteDate | null) => {
    // Convert IsoCompleteDate to calendar date string for URL
    const convertToDateString = (isoDate: IsoCompleteDate) => {
      const dt = DateTime.fromObject({
        weekYear: isoDate.year,
        weekNumber: isoDate.week,
        weekday: (isoDate.day ?? 1) as WeekdayNumbers
      }).set({month: isoDate.month});
      return dt.toFormat('yyyy-MM-dd');
    };

    const startDateString = customStart ? convertToDateString(customStart) : undefined;
    const endDateString = customEnd ? convertToDateString(customEnd) : undefined;

    navigate({
      from: Route.fullPath,
      search: {
        ...searchParams,
        week: date.week,
        year: date.year,
        day: date.day,
        month: date.month,
        timeFrame: timeFrame,
        startDate: startDateString,
        endDate: endDateString,
      }
    });
  };

  const dateRange = getDateRangeForTimeFrame(isoDateSearch, timeFrame, timezone, customStartDate, customEndDate);

  const [searchInput, setSearchInput] = useState(searchParams?.search ?? '');
  const [debouncedSearchInput] = useDebounceValue(searchInput, 750);

  // Debounced function to update URL search params
  const debouncedUpdateSearch = useCallback(
          debounce((value: string) => {
            navigate({
              replace: true,
              search: (prev) => ({
                ...prev,
                search: value
              })
            });
          }, 750),
          [navigate]
  );

  useEffect(() => {
    debouncedUpdateSearch(searchInput);
  }, [searchInput, debouncedUpdateSearch]);

  useEffect(() => {
    setSearchInput(searchParams?.search ?? '');
  }, [searchParams?.search]);

  // Sync sorting state with URL search params
  useEffect(() => {
    if (searchParams?.sorting) {
      setSorting(searchParams.sorting);
    }
  }, [searchParams?.sorting]);

  // Sync column filters state with URL search params
  useEffect(() => {
    if (searchParams?.columnFilters) {
      setColumnFilters(searchParams.columnFilters as ColumnFiltersState);
    }
  }, [searchParams?.columnFilters]);

  // Initialize sorting and column filters from URL search params
  const [sorting, setSorting] = useState<SortingState>(searchParams?.sorting ?? []);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(searchParams?.columnFilters as ColumnFiltersState ?? []);

  // Filter state management
  const filterValues: FilterFormValues = {
    includeArchived: searchParams?.includeArchived ?? false,
  };

  const handleFilterSubmit = useCallback((values: FilterFormValues) => {
    navigate({
      search: (prev) => ({
        ...prev,
        includeArchived: values.includeArchived || undefined,
      }),
      replace: true
    });
  }, [navigate]);

  const handleFilterReset = useCallback(() => {
    navigate({
      search: (prev) => ({
        ...prev,
        includeArchived: undefined,
      }),
      replace: true
    });
  }, [navigate]);

  // Handle sorting changes and update URL search params
  const handleSortingChange = useCallback((sortingState: Updater<SortingState>) => {
    const newSorting = typeof sortingState === "function" ? sortingState(sorting) : sortingState;

    setSorting(newSorting);
    navigate({
      search: (prev) => ({
        ...prev,
        sorting: newSorting.length > 0 ? newSorting : undefined
      }),
      replace: true
    });
  }, [sorting, navigate]);

  // Handle column filter changes and update URL search params
  const handleColumnFiltersChange = useCallback((filtersState: Updater<ColumnFiltersState>) => {
    const newFilters = typeof filtersState === "function" ? filtersState(columnFilters) : filtersState;

    setColumnFilters(newFilters);
    navigate({
      search: (prev) => ({
        ...prev,
        columnFilters: newFilters.length > 0 ? newFilters : undefined
      }),
      replace: true
    });
  }, [columnFilters, navigate]);

  const {data} = api.user.getSchedulingInsightsData.useQuery({
    storeId: storeId!,
    personFilter: debouncedSearchInput.trim(),
    range: dateRange,
    includeArchived: searchParams?.includeArchived ?? false,
  });

  const {peopleData, shiftStats} = useMemo(() => {
    const filteredPeople = filter(schedulePeople, person => filterPersonByName(person, debouncedSearchInput));
    const peopleData = map(filteredPeople, person => {
      const availability = person.availability;
      const hours = calculateAvailableHours(availability, dateRange, timezone);

      const stats = data?.insights ?? {
        personShiftStats: {
          [person.id]: {
            hours: 0,
            shifts: 0,
            adminNonOps: 0,
          }
        },
        personSwapStats: {
          [person.id]: {
            offered: 0,
            received: 0,
          }
        },
        personOfferStats: {
          [person.id]: {
            offered: 0,
            received: 0,
          }
        },
        personTimeOffHours: {
          [person.id]: 0
        },
        totalShifts: 0,
        totalScheduledHours: 0,
      }
      const shiftStats = stats.personShiftStats[person.id] ?? {
        hours: 0,
        shifts: 0,
        adminNonOps: 0,
      };
      const swapStats = stats.personSwapStats[person.id] ?? {
        offered: 0,
        received: 0,
      };
      const offerStats = stats.personOfferStats[person.id] ?? {
        offered: 0,
        received: 0,
      };
      const timeOffHours = stats.personTimeOffHours[person.id] ?? 0;

      return {
        person: person,
        totalAvailability: hours,
        shiftStats: {
          hours: shiftStats.hours,
          shifts: shiftStats.shifts,
          adminNonOps: shiftStats.adminNonOps,
        },
        swapStats: {
          offered: swapStats.offered,
          received: swapStats.received,
        },
        offerStats: {
          offered: offerStats.offered,
          received: offerStats.received,
        },
        timeOffHours,
      };
    });
    return {
      peopleData,
      shiftStats: [
        {title: "Total Scheduled Hours", count: data?.insights.totalScheduledHours ?? 0},
        {title: "Total Shifts", count: data?.insights.totalShifts ?? 0},
        {title: "Total Swaps", count: data?.insights.totalSwaps ?? 0},
        {title: "Total Offers", count: data?.insights.totalOffers ?? 0},
        {title: "Total Time Off Requests", count: data?.insights.totalTimeOffRequests ?? 0},
      ]
    };
  }, [data, schedulePeople]);

  const table = useReactTable({
    data: peopleData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: handleSortingChange,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: handleColumnFiltersChange,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
    onPaginationChange: pagination => {
      const newPagination = typeof pagination === "function" ? pagination({
        pageIndex,
        pageSize
      }) : pagination;
      console.log('Pagination change:', { current: { pageIndex, pageSize }, new: newPagination });
      navigate({
        replace: true,
        search: (prev) => ({
          ...prev,
          pageIndex: newPagination.pageIndex,
          pageSize: newPagination.pageSize
        })
      });
    },
  });

  // Debug logging
  console.log('Table debug:', {
    totalRows: peopleData.length,
    pageIndex,
    pageSize,
    pageCount: table.getPageCount(),
    canNextPage: table.getCanNextPage(),
    canPreviousPage: table.getCanPreviousPage(),
    currentPageRows: table.getRowModel().rows.length
  });

  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  const onExport = (options: {
    availability: boolean,
    scheduled: boolean,
    adminNonOps: boolean,
    shifts: boolean,
    shiftSwaps: boolean,
    shiftOffers: boolean,
    timeOff: boolean,
    itemizeShifts: boolean,
  }) => {
    setIsExportDialogOpen(false);

    // table  -> a copy of how the table is rendered
    // shifts -> report with stats and an itemized list of shifts
    const screen = options.itemizeShifts ? "shifts" : "table";

    // Build URL with all current search parameters including sorting and filtering
    const urlParams = new URLSearchParams();

    // Date and time frame parameters
    if (searchParams?.week) urlParams.set('week', searchParams.week.toString());
    if (searchParams?.year) urlParams.set('year', searchParams.year.toString());
    if (searchParams?.day) urlParams.set('day', searchParams.day.toString());
    if (searchParams?.month) urlParams.set('month', searchParams.month.toString());
    if (timeFrame) urlParams.set('timeFrame', timeFrame);
    if (searchParams?.startDate) urlParams.set('startDate', searchParams.startDate);
    if (searchParams?.endDate) urlParams.set('endDate', searchParams.endDate);

    // Search parameter - use the current debounced search input to ensure consistency
    if (debouncedSearchInput.trim()) urlParams.set('search', debouncedSearchInput.trim());

    // Filter parameters
    if (searchParams?.includeArchived) urlParams.set('includeArchived', searchParams.includeArchived.toString());

    // Column filtering parameters (but not sorting)
    if (columnFilters.length > 0) {
      urlParams.set('columnFilters', JSON.stringify(columnFilters));
    }

    // Export options for conditional column rendering
    urlParams.set('exportOptions', JSON.stringify(options));

    const exportUrl = `${window.location.origin}/${businessId}/${storeId}/print/insights/scheduling/${screen}?${urlParams.toString()}`;
    window.open(exportUrl, '_blank', 'rel=noreferrer noopener');
  }

  return <>
    <div className="w-full min-h-screen bg-gray-50 p-6 flex flex-col gap-4 flex-1">
      <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-col gap-5 justify-start">
        <div className="flex flex-row justify-between">
          <Text className="text-3xl" colorScheme={"dark"} semibold>
            Schedule Insights
          </Text>

          <Button variant={"outline"} leftIcon={<PrinterIcon size={16}/>} onClick={() => setIsExportDialogOpen(true)}>
            Print Report
          </Button>
        </div>

        <div className="flex flex-row flex-wrap justify-between items-center gap-1">
          <div className="flex-shrink-0">
            <CustomDateFilter
                    timezone={store.timezone ?? "America/New_York"}
                    timeRange={timeFrame}
                    selectedDate={isoDateSearch}
                    customStartDate={customStartDate}
                    customEndDate={customEndDate}
                    onDateChange={handleDateChange}
            />
          </div>

          <div className="flex flex-row items-center gap-2">
            <Input type={"search"}
                   leftIcon={SearchIcon}
                   placeholder="Search by name"
                   value={searchInput}
                   onChange={({target}) => {
                     setSearchInput(target.value);
                   }}
                   className="w-72"
            />
            <FilterButton
              initialValues={filterValues}
              onSubmit={handleFilterSubmit}
              onReset={handleFilterReset}
            />
          </div>
        </div>

        <div className={"flex flex-row items-stretch gap-4"}>
          {map(shiftStats, ({title, count}) => {
            return <div key={title}
                        className={"flex-1 border border-gray-200 rounded-lg py-3 px-5 flex flex-col gap-2 justify-between"}>
              <Text>{title}</Text>
              <Text size={"xl"} colorScheme={"dark"} semibold>{cleanNumber(count)}</Text>
            </div>
          })}
        </div>

        <DataTable table={table}
                   rowDetailLinkFrom=""
                   className={"border border-gray-200 rounded-xl overflow-hidden overflow-x-auto"}
                   EmptyComponent={
                     <div className="text-center py-8">
                       <Text muted>No team members found</Text>
                     </div>
                   }
        />
      </div>
    </div>

    <ExportScheduleDataSelectorDialog isOpen={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}
                                      onExport={onExport}/>
  </>
}
